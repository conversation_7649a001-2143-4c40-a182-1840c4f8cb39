{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "barryvdh/laravel-dompdf": "^3.1", "guzzlehttp/guzzle": "^7.2", "illuminate/auth": "*", "jdavidbakr/mail-tracker": "^6.0", "jeroennoten/laravel-adminlte": "^3.14", "laravel/framework": "^9.52", "laravel/sanctum": "^3.0", "laravel/socialite": "^5.21", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "open-admin-org/open-admin": "^1.0", "stripe/stripe-php": "^16.3", "yajra/laravel-datatables": "*", "yajra/laravel-datatables-oracle": "^10.11"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Illuminate\\Foundation\\Auth\\": "vendor/laravel/framework/src/Illuminate/Foundation/Auth/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}